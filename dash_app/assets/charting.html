<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<title>Lightweight Charts — Lines + Fib 50%</title>
<style>
  :root { --bg:#0e1116; --panel:#161b22; --text:#c9d1d9; --muted:#8b949e; --accent:#58a6ff; }
  html, body { height:100%; }
  body { margin:0; display:grid; grid-template-columns: 1fr 320px; height:100%;
         background:var(--bg); color:var(--text); font-family: ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial; }
  #chart { width:100%; height:100%; }
  aside { border-left:1px solid #222; background:var(--panel); display:flex; flex-direction:column; min-width:280px; }
  header, .row { padding:10px 12px; border-bottom:1px solid #222; }
  .btn { background:#222833; color:var(--text); border:1px solid #2b3340; padding:6px 10px; border-radius:8px; cursor:pointer; }
  .btn:hover { border-color:#3a4556; }
  .btn.active { outline:2px solid #58a6ff60; }
  .muted { color:var(--muted); font-size:12px; }
  ul { list-style:none; padding:0; margin:8px 12px 12px; }
  li { display:flex; justify-content:space-between; align-items:center; gap:8px; padding:6px 8px; border:1px solid #2b3340; border-radius:8px; margin-top:8px; }
  .pill { background:#0b1d33; border:1px solid #14304f; border-radius:999px; padding:2px 8px; font-variant-numeric: tabular-nums; }
  .groupTag { font-size:11px; opacity:0.7; border:1px solid #2b3340; border-radius:6px; padding:0 6px; margin-left:6px; }
  .stack { display:flex; gap:8px; flex-wrap:wrap; }
</style>
</head>
<body>
  <div id="chart"></div>

  <aside>
    <header class="stack">
      <button id="mode-normal" class="btn active" title="Normal chart viewing mode">📊 Normal</button>
      <button id="mode-line" class="btn" title="Click to place single line">➕ Line</button>
      <button id="mode-trendline" class="btn" title="Click and drag to draw a diagonal trendline">📈 Trendline</button>
      <button id="mode-fib" class="btn" title="MouseDown set P1, drag, MouseUp set P2, creates retracement and take profit levels">∿ Fib</button>
      <button id="mode-rect" class="btn" title="Click and drag to draw a rectangle">▭ Rectangle</button>
      <button id="clear" class="btn" title="Remove all lines">🧹 Clear</button>
    </header>
    <div class="row">
      <div class="muted" id="hint">Mode: Normal — chart viewing mode with pan and zoom enabled.</div>
    </div>
    <div class="row">
      <strong>Lines</strong>
      <ul id="list"></ul>
    </div>
  </aside>

  <!-- TradingView Lightweight Charts (MIT) -->
  <script src="https://unpkg.com/lightweight-charts@4.2.2/dist/lightweight-charts.standalone.production.js"></script>
  <script>
  (function(){
    // ----- Singleton guard to avoid duplicates on reload -----
    if (window.__lw_chart__) {
      try { window.__lw_chart__.remove(); } catch(e){}
      const chartEl0 = document.getElementById('chart');
      while (chartEl0.firstChild) chartEl0.removeChild(chartEl0.firstChild);
    }

    // ----- Elements -----
    const chartEl = document.getElementById('chart');
    const listEl  = document.getElementById('list');
    const btnNormal = document.getElementById('mode-normal');
    const btnLine = document.getElementById('mode-line');
    const btnTrendline = document.getElementById('mode-trendline');
    const btnFib  = document.getElementById('mode-fib');
    const btnRect = document.getElementById('mode-rect');
    const btnClear= document.getElementById('clear');
    const hintEl  = document.getElementById('hint');

    // ----- Create chart -----
    const chart = window.__lw_chart__ = LightweightCharts.createChart(chartEl, {
      layout: { background: { color: '#0e1116' }, textColor: '#c9d1d9' },
      rightPriceScale: { borderVisible: false, mode: 0 },  // 0 = normal price
      timeScale: { borderVisible: false, timeVisible: true, secondsVisible: false },
      grid: { horzLines: { color: '#1f2633' }, vertLines: { color: '#1f2633' } },
      crosshair: { mode: LightweightCharts.CrosshairMode.Normal },
    });

    // Toggle chart interactions (pan/zoom) while drawing
    function setPanZoomEnabled(enabled) {
      chart.applyOptions({
        handleScroll: enabled,  // disable pressed-mouse pan
        handleScale: enabled,   // disable wheel/pinch zoom
      });
      // Optional: change cursor to signal drawing mode
      chartEl.style.cursor = enabled ? "default" : "crosshair";
    }

    const series = chart.addCandlestickSeries({
      upColor: '#26a69a', downColor: '#ef5350',
      wickUpColor: '#26a69a', wickDownColor: '#ef5350',
      borderVisible: false,
      priceFormat: { type: 'price', precision: 2, minMove: 0.01 },
    });

    // ----- Demo data -----
    const data = [];
    const t0 = Math.floor(Date.now()/1000) - 3600*24*30; // 30d hourly
    let p = 100;
    for (let i=0;i<500;i++){
      const t = t0 + i*3600;
      const o = p, h = o + Math.random()*3+1, l = o - (Math.random()*3+1), c = l + Math.random()*(h-l);
      p = c;
      data.push({ time: t, open:o, high:h, low:l, close:c });
    }
    series.setData(data);
    chart.timeScale().fitContent();

    // ----- State & helpers -----
    const pf = series.options().priceFormat || {};
    const minMove = pf.minMove ?? 0.01;
    const lines = []; // { id, price, handle, kind: 'single'|'fib1'|'fib2'|'fib50'|'fib618'|'fib786'|'fibtp236'|'fibtp618'|'rectangle'|'trendline', group?:string }
    let lastPoint = null; // last crosshair point {x,y}
    let mode = 'normal';    // 'normal' | 'line' | 'trendline' | 'fib' | 'rect'
    const fmt = (x) => Number(x).toLocaleString(undefined, { maximumFractionDigits: 8 });

    // For Rectangle mode
    let rectActive = false;
    let rectStartPoint = null; // {x, y, time, price}
    let tempRectSeries = null;

    // For Trendline mode
    let trendlineActive = false;
    let trendlineStartPoint = null; // {x, y, time, price}
    let tempTrendlineSeries = null;

    // For Fib mode
    let fibActive = false;
    let fibStartPrice = null;
    let tempStart = null; // temp line handles
    let tempEnd = null;

    function snap(price) {
      return Math.round(price / minMove) * minMove;
    }

    function createLine(price, opts = {}) {
      const handle = series.createPriceLine({
        price,
        color: opts.color || '#58a6ff',
        lineWidth: opts.lineWidth ?? 2,
        lineStyle: opts.lineStyle ?? 0, // 0=solid, 1=dotted, 2=dashed
        axisLabelVisible: true,
        title: opts.title ?? String(price),
      });
      const id = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));
      const rec = { id, price, handle, kind: opts.kind || 'single', group: opts.group };
      lines.push(rec);

      // Only add list item for single lines, fib sets, rectangles, and trendlines (not individual fib/rect levels)
      if (opts.kind === 'single' || opts.kind === 'fibset' || opts.kind === 'rectangle' || opts.kind === 'trendline') {
        addListItem(rec);
      }

      post('lw_line_added', { price, kind: rec.kind, group: rec.group });
      return rec;
    }

    function removeLineById(id) {
      const idx = lines.findIndex(l => l.id === id);
      if (idx === -1) return;
      const rec = lines[idx];

      // Handle different types of chart elements
      if (rec.handle) {
        // Remove price line (rectangles and trendlines don't have handles, they're managed by group removal)
        try { series.removePriceLine(rec.handle); } catch(e){}
      }

      // Handle trendline series removal
      if (rec.kind === 'trendline' && rec.trendlineSeries) {
        try { chart.removeSeries(rec.trendlineSeries); } catch(e){}
      }

      lines.splice(idx, 1);
      const el = document.getElementById('li-' + id);
      if (el) el.remove();
      post('lw_line_removed', { id, kind: rec.kind, group: rec.group });
    }

    function removeGroup(groupId) {
      const groupLines = lines.filter(l => l.group === groupId);

      // Handle rectangle markers removal
      const rectangleInGroup = groupLines.find(l => l.kind === 'rectangle');
      if (rectangleInGroup && rectangleInGroup.markers) {
        try {
          // Get existing markers and filter out the rectangle's markers
          const existingMarkers = series.markers?.() || [];
          const filteredMarkers = existingMarkers.filter(marker =>
            !rectangleInGroup.markers.some(rectMarker =>
              rectMarker.time === marker.time && rectMarker.text === marker.text
            )
          );
          series.setMarkers(filteredMarkers);
        } catch(e) {
          console.log('Error removing rectangle markers:', e);
        }
      }

      // Handle trendline series removal
      const trendlineInGroup = groupLines.find(l => l.kind === 'trendline');
      if (trendlineInGroup && trendlineInGroup.trendlineSeries) {
        try {
          chart.removeSeries(trendlineInGroup.trendlineSeries);
        } catch(e) {
          console.log('Error removing trendline series:', e);
        }
      }

      const toRemove = groupLines.map(l => l.id);
      toRemove.forEach(removeLineById);
    }

    function addListItem(rec) {
      const li = document.createElement('li');
      li.id = 'li-' + rec.id;

      const left = document.createElement('div');
      if (rec.kind === 'fibset') {
        left.textContent = 'Fib Set ';
        const pill = document.createElement('span');
        pill.className='pill';
        pill.textContent = `P1: ${fmt(rec.p1)} → P2: ${fmt(rec.p2)}`;
        left.appendChild(pill);
      } else if (rec.kind === 'rectangle') {
        left.textContent = 'Rectangle ';
        const pill = document.createElement('span');
        pill.className='pill';
        pill.textContent = `${fmt(rec.p1)} → ${fmt(rec.p2)}`;
        left.appendChild(pill);
      } else if (rec.kind === 'trendline') {
        left.textContent = 'Trendline ';
        const pill = document.createElement('span');
        pill.className='pill';
        pill.textContent = `${fmt(rec.p1)} → ${fmt(rec.p2)}`;
        left.appendChild(pill);
      } else {
        left.textContent = (rec.kind === 'single') ? 'Line @ ' : 'Line @ ';
        const pill = document.createElement('span'); pill.className='pill'; pill.textContent = fmt(rec.price);
        left.appendChild(pill);
      }

      if (rec.group && (rec.kind === 'fibset' || rec.kind === 'rectangle' || rec.kind === 'trendline')) {
        const tag = document.createElement('span');
        tag.className = 'groupTag';
        tag.textContent = rec.group.slice(0,6);
        left.appendChild(tag);
      }

      const right = document.createElement('div');

      if (rec.kind === 'fibset' || rec.kind === 'rectangle' || rec.kind === 'trendline') {
        // For fib sets, rectangles, and trendlines, only show delete button (no copy since it's multiple values)
        const del = document.createElement('button'); del.className='btn'; del.textContent='🗑️';
        del.onclick = () => removeGroup(rec.group);
        right.appendChild(del);
      } else {
        // For single lines, show both copy and delete
        const copy = document.createElement('button'); copy.className='btn'; copy.textContent='Copy';
        copy.onclick = () => { navigator.clipboard.writeText(String(rec.price)); copy.textContent='✔'; setTimeout(()=>copy.textContent='Copy',800); };

        const del = document.createElement('button'); del.className='btn'; del.textContent='🗑️';
        del.onclick = () => removeLineById(rec.id);

        right.append(copy, del);
      }

      li.append(left, right);
      listEl.appendChild(li);
    }

    function post(type, payload) {
      try { window.parent?.postMessage({ type, ...payload }, '*'); } catch(e){}
    }

    // Helper function to calculate trendline price at a given time
    function calculateTrendlinePrice(startTime, startPrice, endTime, endPrice, targetTime) {
      if (startTime === endTime) return startPrice; // Avoid division by zero
      const slope = (endPrice - startPrice) / (endTime - startTime);
      return startPrice + slope * (targetTime - startTime);
    }

    // Helper function to create trendline series
    function createTrendlineSeries(startPoint, endPoint, opts = {}) {
      const trendlineSeries = chart.addLineSeries({
        color: opts.color || '#ff6b6b',
        lineWidth: opts.lineWidth || 2,
        lineStyle: opts.lineStyle || 0, // solid
        lastValueVisible: false,
        priceLineVisible: false,
        title: opts.title || 'Trendline'
      });

      // Set the two points for the trendline
      trendlineSeries.setData([
        { time: startPoint.time, value: startPoint.price },
        { time: endPoint.time, value: endPoint.price }
      ]);

      return trendlineSeries;
    }

    // Track last crosshair point so we always have exact y
    chart.subscribeCrosshairMove((param) => {
      if (param?.point && typeof param.point.y === 'number') {
        lastPoint = param.point;

        // live-update temp trendline
        if (trendlineActive && trendlineStartPoint) {
          const currentTime = chart.timeScale().coordinateToTime(lastPoint.x);
          const currentPrice = snap(series.coordinateToPrice(lastPoint.y));

          // Remove existing temp trendline
          if (tempTrendlineSeries) {
            try { chart.removeSeries(tempTrendlineSeries); } catch(e){}
            tempTrendlineSeries = null;
          }

          // Create new temp trendline
          tempTrendlineSeries = createTrendlineSeries(
            { time: trendlineStartPoint.time, price: trendlineStartPoint.price },
            { time: currentTime, price: currentPrice },
            { color: '#cccccc', lineWidth: 1, lineStyle: 2, title: 'Trendline?' }
          );
        }

        // live-update temp fib end line
        if (fibActive) {
          const y = lastPoint.y;
          const price = snap(series.coordinateToPrice(y));
          // re-create dynamic end line
          if (tempEnd) { try { series.removePriceLine(tempEnd); } catch(e){} tempEnd = null; }
          tempEnd = series.createPriceLine({
            price,
            color:'#cccccc', lineWidth:1, lineStyle:2, axisLabelVisible:true, title:'P2?'
          });
        }

        // live-update temp rectangle
        if (rectActive && rectStartPoint && tempRectSeries) {
          const currentTime = chart.timeScale().coordinateToTime(lastPoint.x);
          const currentPrice = snap(series.coordinateToPrice(lastPoint.y));
          const topPrice = Math.max(rectStartPoint.price, currentPrice);
          const bottomPrice = Math.min(rectStartPoint.price, currentPrice);
          const leftTime = Math.min(rectStartPoint.time, currentTime);
          const rightTime = Math.max(rectStartPoint.time, currentTime);

          // Remove existing temp lines and markers
          if (tempRectSeries.top) { try { series.removePriceLine(tempRectSeries.top); } catch(e){} }
          if (tempRectSeries.bottom) { try { series.removePriceLine(tempRectSeries.bottom); } catch(e){} }
          if (tempRectSeries.leftMarker) { try { series.removeMarkers([tempRectSeries.leftMarker]); } catch(e){} }
          if (tempRectSeries.rightMarker) { try { series.removeMarkers([tempRectSeries.rightMarker]); } catch(e){} }

          // Create new temp horizontal lines
          tempRectSeries.top = series.createPriceLine({
            price: topPrice,
            color: '#58a6ff',
            lineWidth: 2,
            lineStyle: 2, // dashed
            axisLabelVisible: true,
            title: 'Rect Top'
          });

          tempRectSeries.bottom = series.createPriceLine({
            price: bottomPrice,
            color: '#58a6ff',
            lineWidth: 2,
            lineStyle: 2, // dashed
            axisLabelVisible: true,
            title: 'Rect Bottom'
          });

          // Create vertical markers for left and right edges
          const leftMarker = {
            time: leftTime,
            position: 'inBar',
            color: '#58a6ff',
            shape: 'arrowUp',
            text: '│',
            size: 0
          };

          const rightMarker = {
            time: rightTime,
            position: 'inBar',
            color: '#58a6ff',
            shape: 'arrowUp',
            text: '│',
            size: 0
          };

          try {
            series.setMarkers([leftMarker, rightMarker]);
            tempRectSeries.leftMarker = leftMarker;
            tempRectSeries.rightMarker = rightMarker;
          } catch(e) {
            console.log('Marker error:', e);
          }
        }
      }
    });

    // ----- Modes -----
    function setMode(newMode){
      mode = newMode;
      btnNormal.classList.toggle('active', mode==='normal');
      btnLine.classList.toggle('active', mode==='line');
      btnTrendline.classList.toggle('active', mode==='trendline');
      btnFib.classList.toggle('active',  mode==='fib');
      btnRect.classList.toggle('active', mode==='rect');

      // Enable/disable pan and zoom based on mode
      setPanZoomEnabled(mode === 'normal');

      hintEl.textContent = (mode==='normal')
          ? 'Mode: Normal — chart viewing mode with pan and zoom enabled.'
          : (mode==='line')
          ? 'Mode: Line — click anywhere to drop a horizontal line at the exact price.'
          : (mode==='trendline')
          ? 'Mode: Trendline — click and drag to draw a diagonal trendline. Useful for trend analysis and support/resistance levels.'
          : (mode==='rect')
          ? 'Mode: Rectangle — click and drag to draw a rectangle. Useful for highlighting price ranges or time periods.'
          : 'Mode: Fib — mouse down to set P1, drag, mouse up to set P2. Creates P1, P2, 50%, 61.8%, 78.6% retracements and -23.6%, -61.8% take profit levels. Shift+click trash on any fib level to delete the whole set.';
    }
    btnNormal.onclick = () => setMode('normal');
    btnLine.onclick = () => setMode('line');
    btnTrendline.onclick = () => setMode('trendline');
    btnFib.onclick  = () => setMode('fib');
    btnRect.onclick = () => setMode('rect');
    setMode('normal');

    // ----- Click → single line (only in Line mode) -----
    chart.subscribeClick((param) => {
      if (mode !== 'line') return;
      if (!param?.point || typeof param.point.y !== 'number') return;
      let price = snap(series.coordinateToPrice(param.point.y));
      createLine(price, { color:'#58a6ff', kind:'single' });
      console.log('Line placed at', price);

      // Automatically switch back to normal mode after placing a line
      setMode('normal');
    });

    // ----- Trendline, Fib & Rectangle: mousedown starts drawing -----
    chartEl.addEventListener('mousedown', (e) => {
      if (mode === 'trendline') {
        if (!lastPoint || typeof lastPoint.y !== 'number') return;

        // Disable pan/zoom while dragging Trendline
        setPanZoomEnabled(false);

        trendlineActive = true;
        const time = chart.timeScale().coordinateToTime(lastPoint.x);
        const price = snap(series.coordinateToPrice(lastPoint.y));
        trendlineStartPoint = { x: lastPoint.x, y: lastPoint.y, time, price };

        // prevent the chart from seeing this as a pan start
        e.preventDefault();
        e.stopPropagation();
      } else if (mode === 'fib') {
        if (!lastPoint || typeof lastPoint.y !== 'number') return;

        // Disable pan/zoom while dragging Fib
        setPanZoomEnabled(false);

        fibActive = true;
        fibStartPrice = snap(series.coordinateToPrice(lastPoint.y));

        // temp start line
        tempStart = series.createPriceLine({
          price: fibStartPrice, color:'#cccccc', lineWidth:1, lineStyle:2, axisLabelVisible:true, title:'P1'
        });

        // prevent the chart from seeing this as a pan start
        e.preventDefault();
        e.stopPropagation();
      } else if (mode === 'rect') {
        if (!lastPoint || typeof lastPoint.y !== 'number') return;

        // Disable pan/zoom while dragging Rectangle
        setPanZoomEnabled(false);

        rectActive = true;
        const time = chart.timeScale().coordinateToTime(lastPoint.x);
        const price = snap(series.coordinateToPrice(lastPoint.y));
        rectStartPoint = { x: lastPoint.x, y: lastPoint.y, time, price };

        // Create temporary rectangle lines for preview
        tempRectSeries = {
          top: null,
          bottom: null,
          leftMarker: null,
          rightMarker: null,
        };

        // prevent the chart from seeing this as a pan start
        e.preventDefault();
        e.stopPropagation();
      }
    });

    const endFib = () => {
      if (!fibActive) return;
      fibActive = false;

      // finalize P2
      if (!lastPoint || typeof lastPoint.y !== 'number') {
        // cleanup temps
        if (tempStart) { try { series.removePriceLine(tempStart); } catch(e){} tempStart=null; }
        if (tempEnd)   { try { series.removePriceLine(tempEnd); } catch(e){} tempEnd=null; }
        return;
      }
      const fibEndPrice = snap(series.coordinateToPrice(lastPoint.y));
      const group = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));

      // remove temps
      if (tempStart) { try { series.removePriceLine(tempStart); } catch(e){} tempStart=null; }
      if (tempEnd)   { try { series.removePriceLine(tempEnd); } catch(e){} tempEnd=null; }

      // create permanent lines (no list items for individual lines)
      const p1 = createLine(fibStartPrice, { color:'#f0ad4e', lineWidth:2, kind:'fib1', group, title:'P1' });
      const p2 = createLine(fibEndPrice,   { color:'#f0ad4e', lineWidth:2, kind:'fib2', group, title:'P2' });

      // Calculate Fibonacci levels
      const range = fibEndPrice - fibStartPrice;
      const fib50 = snap(fibStartPrice + range * 0.5);
      const fib618 = snap(fibStartPrice + range * 0.618);
      const fib786 = snap(fibStartPrice + range * 0.786);

      // Calculate take profit levels (extensions beyond P2)
      const fibtp236 = snap(fibEndPrice + range * 0.236);
      const fibtp618 = snap(fibEndPrice + range * 0.618);

      // Create retracement lines (no list items for individual lines)
      const p50 = createLine(fib50, { color:'#9ed0ff', lineWidth:2, lineStyle:2, kind:'fib50', group, title:'50%' }); // dashed
      const p618 = createLine(fib618, { color:'#9ed0ff', lineWidth:2, lineStyle:2, kind:'fib618', group, title:'61.8%' }); // dashed
      const p786 = createLine(fib786, { color:'#9ed0ff', lineWidth:2, lineStyle:2, kind:'fib786', group, title:'78.6%' }); // dashed

      // Create take profit lines (dotted, no list items for individual lines)
      const ptp236 = createLine(fibtp236, { color:'#00ff88', lineWidth:2, lineStyle:1, kind:'fibtp236', group, title:'TP -23.6%' }); // dotted
      const ptp618 = createLine(fibtp618, { color:'#00ff88', lineWidth:2, lineStyle:1, kind:'fibtp618', group, title:'TP -61.8%' }); // dotted

      // Create a single list entry for the entire Fibonacci set
      const fibSetId = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));
      const fibSetRec = { id: fibSetId, p1: fibStartPrice, p2: fibEndPrice, kind: 'fibset', group, handle: null };
      lines.push(fibSetRec);
      addListItem(fibSetRec);

      console.log('Fib set: P1=', fibStartPrice, ' P2=', fibEndPrice, ' levels=', { fib50, fib618, fib786, fibtp236, fibtp618 });
      post('lw_fib_added', { p1:fibStartPrice, p2:fibEndPrice, fib50, fib618, fib786, fibtp236, fibtp618 });
      fibStartPrice = null;
    };

    // mouseup on whole doc so it completes even if cursor leaves chart
    document.addEventListener('mouseup', () => {
      if (!trendlineActive && !fibActive && !rectActive) return;

      // Re-enable pan/zoom now that we’re done
      setPanZoomEnabled(true);

      if (trendlineActive) {
        trendlineActive = false;

        if (!lastPoint || typeof lastPoint.y !== 'number' || !trendlineStartPoint) {
          // cleanup temp trendline
          if (tempTrendlineSeries) {
            try { chart.removeSeries(tempTrendlineSeries); } catch(e){}
            tempTrendlineSeries = null;
          }
          trendlineStartPoint = null;
          return;
        }

        const currentTime = chart.timeScale().coordinateToTime(lastPoint.x);
        const currentPrice = snap(series.coordinateToPrice(lastPoint.y));
        const group = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));

        // Remove temp trendline
        if (tempTrendlineSeries) {
          try { chart.removeSeries(tempTrendlineSeries); } catch(e){}
          tempTrendlineSeries = null;
        }

        // Create permanent trendline
        const permanentTrendlineSeries = createTrendlineSeries(
          { time: trendlineStartPoint.time, price: trendlineStartPoint.price },
          { time: currentTime, price: currentPrice },
          { color: '#ff6b6b', lineWidth: 2, title: 'Trendline' }
        );

        // Create a single list entry for the trendline
        const trendlineId = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));
        const trendlineRec = {
          id: trendlineId,
          p1: trendlineStartPoint.price,
          p2: currentPrice,
          t1: trendlineStartPoint.time,
          t2: currentTime,
          kind: 'trendline',
          group,
          handle: null,
          trendlineSeries: permanentTrendlineSeries
        };
        lines.push(trendlineRec);
        addListItem(trendlineRec);

        console.log('Trendline created:', {
          p1: trendlineStartPoint.price,
          p2: currentPrice,
          t1: trendlineStartPoint.time,
          t2: currentTime,
          // Calculate current price intersection for demonstration
          currentIntersection: calculateTrendlinePrice(
            trendlineStartPoint.time,
            trendlineStartPoint.price,
            currentTime,
            currentPrice,
            Math.floor(Date.now() / 1000) // current timestamp
          )
        });

        post('lw_trendline_added', {
          p1: trendlineStartPoint.price,
          p2: currentPrice,
          t1: trendlineStartPoint.time,
          t2: currentTime,
          group
        });

        trendlineStartPoint = null;

        // Automatically switch back to normal mode after completing trendline
        setMode('normal');
      }

      if (fibActive) {
        fibActive = false;

      if (!lastPoint || typeof lastPoint.y !== 'number') {
        if (tempStart) { try { series.removePriceLine(tempStart); } catch(e){} tempStart=null; }
        if (tempEnd)   { try { series.removePriceLine(tempEnd); } catch(e){} tempEnd=null; }
        return;
      }

      const fibEndPrice = snap(series.coordinateToPrice(lastPoint.y));
      const group = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));

      if (tempStart) { try { series.removePriceLine(tempStart); } catch(e){} tempStart=null; }
      if (tempEnd)   { try { series.removePriceLine(tempEnd); } catch(e){} tempEnd=null; }

      createLine(fibStartPrice, { color:'#f0ad4e', lineWidth:2, kind:'fib1', group, title:'P1' });
      createLine(fibEndPrice,   { color:'#f0ad4e', lineWidth:2, kind:'fib2', group, title:'P2' });

      // Calculate Fibonacci levels
      const range = fibEndPrice - fibStartPrice;
      const fib50 = snap(fibStartPrice + range * 0.5);
      const fib618 = snap(fibStartPrice + range * 0.618);
      const fib786 = snap(fibStartPrice + range * 0.786);

      // Calculate take profit levels (extensions beyond P2)
      const fibtp236 = snap(fibEndPrice + range * 0.236);
      const fibtp618 = snap(fibEndPrice + range * 0.618);

      // Create retracement lines (no list items for individual lines)
      createLine(fib50, { color:'#9ed0ff', lineWidth:2, lineStyle:2, kind:'fib50', group, title:'50%' }); // dashed
      createLine(fib618, { color:'#9ed0ff', lineWidth:2, lineStyle:2, kind:'fib618', group, title:'61.8%' }); // dashed
      createLine(fib786, { color:'#9ed0ff', lineWidth:2, lineStyle:2, kind:'fib786', group, title:'78.6%' }); // dashed

      // Create take profit lines (dotted, no list items for individual lines)
      createLine(fibtp236, { color:'#00ff88', lineWidth:2, lineStyle:1, kind:'fibtp236', group, title:'TP -23.6%' }); // dotted
      createLine(fibtp618, { color:'#00ff88', lineWidth:2, lineStyle:1, kind:'fibtp618', group, title:'TP -61.8%' }); // dotted

      // Create a single list entry for the entire Fibonacci set
      const fibSetId = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));
      const fibSetRec = { id: fibSetId, p1: fibStartPrice, p2: fibEndPrice, kind: 'fibset', group, handle: null };
      lines.push(fibSetRec);
      addListItem(fibSetRec);

      console.log('Fib set:', { p1:fibStartPrice, p2:fibEndPrice, fib50, fib618, fib786, fibtp236, fibtp618 });
      post('lw_fib_added', { p1:fibStartPrice, p2:fibEndPrice, fib50, fib618, fib786, fibtp236, fibtp618 });
      fibStartPrice = null;

        // Automatically switch back to normal mode after completing Fibonacci retracement
        setMode('normal');
      } else if (rectActive) {
        rectActive = false;

        if (!lastPoint || typeof lastPoint.y !== 'number' || !rectStartPoint) {
          // cleanup temp rectangle
          if (tempRectSeries) {
            if (tempRectSeries.top) { try { series.removePriceLine(tempRectSeries.top); } catch(e){} }
            if (tempRectSeries.bottom) { try { series.removePriceLine(tempRectSeries.bottom); } catch(e){} }
            try { series.setMarkers([]); } catch(e){} // Clear markers
            tempRectSeries = null;
          }
          rectStartPoint = null;
          return;
        }

        const currentTime = chart.timeScale().coordinateToTime(lastPoint.x);
        const currentPrice = snap(series.coordinateToPrice(lastPoint.y));
        const topPrice = Math.max(rectStartPoint.price, currentPrice);
        const bottomPrice = Math.min(rectStartPoint.price, currentPrice);
        const leftTime = Math.min(rectStartPoint.time, currentTime);
        const rightTime = Math.max(rectStartPoint.time, currentTime);
        const group = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));

        // Remove temp rectangle lines and markers
        if (tempRectSeries) {
          if (tempRectSeries.top) { try { series.removePriceLine(tempRectSeries.top); } catch(e){} }
          if (tempRectSeries.bottom) { try { series.removePriceLine(tempRectSeries.bottom); } catch(e){} }
          try { series.setMarkers([]); } catch(e){} // Clear temp markers
          tempRectSeries = null;
        }

        // Create permanent rectangle lines (no individual list items)
        const topLine = createLine(topPrice, { color:'#58a6ff', lineWidth:2, kind:'rect_top', group, title:'Rect Top' });
        const bottomLine = createLine(bottomPrice, { color:'#58a6ff', lineWidth:2, kind:'rect_bottom', group, title:'Rect Bottom' });

        // Create permanent vertical markers for the rectangle edges
        const permanentMarkers = [
          {
            time: leftTime,
            position: 'inBar',
            color: '#58a6ff',
            shape: 'circle',
            text: '│',
            size: 1
          },
          {
            time: rightTime,
            position: 'inBar',
            color: '#58a6ff',
            shape: 'circle',
            text: '│',
            size: 1
          }
        ];

        try {
          // Get existing markers and add our rectangle markers
          const existingMarkers = series.markers?.() || [];
          series.setMarkers([...existingMarkers, ...permanentMarkers]);
        } catch(e) {
          console.log('Permanent marker error:', e);
        }

        // Create a single list entry for the rectangle
        const rectId = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));
        const rectRec = {
          id: rectId,
          p1: rectStartPoint.price,
          p2: currentPrice,
          t1: leftTime,
          t2: rightTime,
          kind: 'rectangle',
          group,
          handle: null,
          markers: permanentMarkers
        };
        lines.push(rectRec);
        addListItem(rectRec);

        console.log('Rectangle created:', { p1: rectStartPoint.price, p2: currentPrice, t1: leftTime, t2: rightTime });
        post('lw_rectangle_added', { p1: rectStartPoint.price, p2: currentPrice, t1: leftTime, t2: rightTime, group });
        rectStartPoint = null;

        // Automatically switch back to normal mode after completing rectangle
        setMode('normal');
      }
    });

    // ----- Clear all -----
    btnClear.onclick = () => {
      // remove all lines & list
      for (const l of [...lines]) {
        try { series.removePriceLine(l.handle); } catch(e){}
      }
      lines.length = 0;
      listEl.innerHTML = '';
      post('lw_lines_cleared', {});
    };

    // ----- Responsive -----
    const ro = new ResizeObserver(entries=>{
      const { width, height } = entries[0].contentRect;
      chart.applyOptions({ width, height });
    });
    ro.observe(chartEl);
  })();
  </script>
</body>
</html>
